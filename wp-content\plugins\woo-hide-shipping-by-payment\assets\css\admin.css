/**
 * Admin styles for WooCommerce Hide Shipping by Payment Method
 */

/* Header section */
.woo-hide-shipping-header {
    margin: 20px 0;
    padding: 15px 0;
    border-bottom: 1px solid #ddd;
}

.woo-hide-shipping-header .button-primary {
    font-size: 14px;
    padding: 8px 16px;
}

/* Form styling */
.woo-hide-shipping-form {
    max-width: 800px;
}

.woo-hide-shipping-form .form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
}

.woo-hide-shipping-form .form-table td {
    padding: 15px 10px;
}

.woo-hide-shipping-select {
    min-width: 300px;
    min-height: 120px;
    width: 100%;
    max-width: 500px;
}

/* Status indicators */
.status-active {
    color: #46b450;
    font-weight: bold;
}

.status-inactive {
    color: #dc3232;
    font-weight: bold;
}

/* Table styling */
.wp-list-table .column-rule_name {
    width: 20%;
}

.wp-list-table .column-payment_methods {
    width: 25%;
}

.wp-list-table .column-shipping_methods {
    width: 25%;
}

.wp-list-table .column-status {
    width: 10%;
}

.wp-list-table .column-actions {
    width: 20%;
}

/* Action buttons */
.wp-list-table .button-small {
    margin-right: 5px;
    margin-bottom: 5px;
}

.wp-list-table .button-link-delete {
    color: #a00;
    text-decoration: none;
    border: none;
    background: none;
    cursor: pointer;
    padding: 0;
    font-size: inherit;
}

.wp-list-table .button-link-delete:hover {
    color: #dc3232;
    text-decoration: underline;
}

/* Loading states */
.woo-hide-shipping-loading {
    opacity: 0.6;
    pointer-events: none;
}

.woo-hide-shipping-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: woo-hide-shipping-spin 1s linear infinite;
}

@keyframes woo-hide-shipping-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media screen and (max-width: 782px) {
    .woo-hide-shipping-select {
        min-width: 100%;
    }

    .wp-list-table .column-payment_methods,
    .wp-list-table .column-shipping_methods {
        display: none;
    }

    .woo-hide-shipping-form .form-table th,
    .woo-hide-shipping-form .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }

    .woo-hide-shipping-form .form-table th {
        border-bottom: none;
    }
}

/* Notice styling */
.woo-hide-shipping-notice {
    margin: 15px 0;
    padding: 12px;
    border-left: 4px solid #0073aa;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.woo-hide-shipping-notice.notice-error {
    border-left-color: #dc3232;
}

.woo-hide-shipping-notice.notice-success {
    border-left-color: #46b450;
}

.woo-hide-shipping-notice.notice-warning {
    border-left-color: #ffb900;
}

/* Multi-select styling improvements */
.woo-hide-shipping-select option {
    padding: 5px 8px;
}

.woo-hide-shipping-select option:checked {
    background: #0073aa;
    color: white;
}

/* Form validation */
.woo-hide-shipping-form .required-field {
    border-color: #dc3232;
}

.woo-hide-shipping-form .field-error {
    color: #dc3232;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}