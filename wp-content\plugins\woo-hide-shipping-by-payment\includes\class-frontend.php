<?php
/**
 * Frontend functionality for WooCommerce Hide Shipping by Payment Method
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooHideShipping_Frontend {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_filter('woocommerce_package_rates', array($this, 'hide_shipping_methods'), 100, 2);
        add_action('woocommerce_checkout_update_order_review', array($this, 'checkout_update_order_review'));
    }

    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        if (is_checkout()) {
            wp_enqueue_script(
                'woo-hide-shipping-frontend',
                WOO_HIDE_SHIPPING_PLUGIN_URL . 'assets/js/frontend.js',
                array('jquery'),
                WOO_HIDE_SHIPPING_VERSION,
                true
            );

            wp_localize_script('woo-hide-shipping-frontend', 'wooHideShippingFrontend', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('woo_hide_shipping_frontend_nonce')
            ));
        }
    }

    /**
     * Hide shipping methods based on selected payment method
     */
    public function hide_shipping_methods($rates, $package) {
        // Get the selected payment method
        $chosen_payment_method = WC()->session->get('chosen_payment_method');

        if (empty($chosen_payment_method)) {
            return $rates;
        }

        // Get rules for this payment method
        $rules = WooHideShipping_Database::get_rules_for_payment_method($chosen_payment_method);

        if (empty($rules)) {
            return $rates;
        }

        // Collect all shipping methods to hide
        $methods_to_hide = array();
        foreach ($rules as $rule) {
            if (is_array($rule->shipping_methods)) {
                $methods_to_hide = array_merge($methods_to_hide, $rule->shipping_methods);
            }
        }

        // Remove duplicate methods
        $methods_to_hide = array_unique($methods_to_hide);

        // Filter out the shipping methods that should be hidden
        foreach ($rates as $rate_id => $rate) {
            // Check if this rate should be hidden
            if ($this->should_hide_shipping_method($rate, $methods_to_hide)) {
                unset($rates[$rate_id]);
            }
        }

        return $rates;
    }

    /**
     * Check if a shipping method should be hidden
     */
    private function should_hide_shipping_method($rate, $methods_to_hide) {
        foreach ($methods_to_hide as $method_to_hide) {
            // Check exact match
            if ($rate->id === $method_to_hide) {
                return true;
            }

            // Check method type match (e.g., 'flat_rate' matches 'flat_rate:1')
            if (strpos($rate->id, $method_to_hide . ':') === 0) {
                return true;
            }

            // Check if the method ID contains the method to hide
            if (strpos($rate->id, $method_to_hide) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Handle checkout update order review
     */
    public function checkout_update_order_review($post_data) {
        // Parse the post data
        parse_str($post_data, $data);

        // Check if payment method has changed
        if (isset($data['payment_method'])) {
            $chosen_payment_method = sanitize_text_field($data['payment_method']);
            WC()->session->set('chosen_payment_method', $chosen_payment_method);

            // Trigger shipping calculation refresh
            WC()->cart->calculate_shipping();
        }
    }

    /**
     * Get shipping methods that should be hidden for current payment method
     */
    public function get_hidden_methods_for_payment($payment_method) {
        $rules = WooHideShipping_Database::get_rules_for_payment_method($payment_method);
        $methods_to_hide = array();

        if (!empty($rules)) {
            foreach ($rules as $rule) {
                if (is_array($rule->shipping_methods)) {
                    $methods_to_hide = array_merge($methods_to_hide, $rule->shipping_methods);
                }
            }
        }

        return array_unique($methods_to_hide);
    }
}