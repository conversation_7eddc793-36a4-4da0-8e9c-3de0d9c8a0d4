[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:55 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:16:55 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Aggiungendo pulsanti alla colonna azioni per ordine: 29037
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-refunded
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-refunded
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-refunded, Pulsanti trovati: 0
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:16:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => false
)

[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:16:57 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:58 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:16:58 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:03 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:03 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:03 UTC] WAA: POST data: Array
(
    [_wpnonce] => 2b374eb690
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-refunded
    [referredby] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [_wp_original_http_referer] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [post_ID] => 29037
    [meta-box-order-nonce] => e33866cbd9
    [closedpostboxesnonce] => 935da108e9
    [original_post_title] => Order &ndash; Agosto 13, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => e7300fba31
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 956ffdfb92
    [post_status] => refunded
    [order_date] => 2025-08-13
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-processing
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 7a1c696d40
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => cb5e5ef417
)

[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:06 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098218:1
)

[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Aggiungendo pulsanti alla colonna azioni per ordine: 29037
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:19 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:29 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:41 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:52 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:20 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => false
)

[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:19:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:19:53 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:20:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:20:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:20:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:20:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:21:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:21:54 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:22:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:22:07 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:23:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:23:55 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:08 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:34 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:35 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:38 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:43 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:43 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:47 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:47 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:01 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=2
    [action] => wosb_save_button
    [button_id] => 2
    [button_name] => Rimborsato
    [button_description] => rimborsa
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-refunded
    [dashicon_code] => \\f476
    [email_content] => test
)

[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:02 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:02 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:07 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:07 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:17 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:25:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:21 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:21 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:34 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=2
    [action] => wosb_save_button
    [button_id] => 2
    [button_name] => Rimborsato
    [button_description] => rimborsa
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-refunded
    [dashicon_code] => \\f528
    [email_content] => test
)

[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:35 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:23 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=add
    [action] => wosb_save_button
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\f528
    [email_content] => dqdqwdqwdqw
)

[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:24 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:29 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:32 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:42 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:28:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:28:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:28:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:28:43 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:02 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:02 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:02 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:16 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=3
    [action] => wosb_save_button
    [button_id] => 3
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\f528
    [email_content] => dqdqwdqwdqw
)

[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:24 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Email inviata con successo per l'ordine #29037
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:33 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-failed, Pulsanti trovati: 0
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:36 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:45 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:47 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:49 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:49 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:51 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098225:1
)

[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:54 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:54 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:54 UTC] WAA: POST data: Array
(
    [_wpnonce] => 2b374eb690
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-failed
    [referredby] => 
    [_wp_original_http_referer] => 
    [post_ID] => 29037
    [meta-box-order-nonce] => e33866cbd9
    [closedpostboxesnonce] => 935da108e9
    [original_post_title] => Order &ndash; Agosto 13, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => e7300fba31
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 956ffdfb92
    [post_status] => failed
    [order_date] => 2025-08-13
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-on-hold
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 7a1c696d40
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => cb5e5ef417
)

[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:56 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:29:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:58 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098989:1
)

[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:01 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:01 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:03 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:15 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:20 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:21 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:21 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:23 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:23 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:23 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[13-Aug-2025 15:30:24 UTC] WEAB: Added 5 buttons for order 29037
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:25 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:25 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:35 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:35 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:42 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:42 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:42 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:59 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:31:18 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:31:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:31:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:31:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:31:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:31:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:31:44 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:31:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:31:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:32:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:32:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:32:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:33:00 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:33:00 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:33:00 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:33:18 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:33:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:33:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:33:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:33:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:33:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:33:44 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:33:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:33:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:34:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:34:25 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:34:25 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:34:25 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:34:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:34:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:34:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:34:26 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:34:26 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:34:26 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:34:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:34:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:34:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:35:01 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:35:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:35:01 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:35:19 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:35:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:35:19 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:35:28 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:35:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:35:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:37:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:37:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:37:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:37:02 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:37:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:37:02 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:37:20 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:37:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:37:20 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:37:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:37:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:37:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:37:28 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:37:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:37:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:39:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:39:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:39:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:39:03 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:39:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:39:03 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:39:21 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:39:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:39:21 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:39:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:39:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:39:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:39:29 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:39:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:39:29 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:41:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:41:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:41:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:41:04 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:41:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:41:04 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:41:22 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:41:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:41:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:41:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:41:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:41:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:41:30 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:41:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:41:30 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:43:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:43:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:43:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:43:05 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:43:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:43:05 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:43:23 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:43:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:43:23 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:43:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:43:31 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:43:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:43:31 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:44:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:44:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:44:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:44:57 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:44:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:44:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => true
)

[13-Aug-2025 15:44:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:44:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:44:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:44:59 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:44:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:44:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:03 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:45:03 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:03 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:45:05 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:45:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:09 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:09 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:19 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:19 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:45:24 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:45:24 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:45:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:45:53 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=3
    [action] => wosb_save_button
    [button_id] => 3
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\\\f528
    [email_subject] => Ordine {order_number} Fallito
    [email_content] => {order_number}dqdqwdqwdqw
)

[13-Aug-2025 15:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:45:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:45:54 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:45:54 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:46:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:46:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:46:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:46:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:46:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:47:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:47:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:47:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:47:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:47:06 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:47:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:47:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:47:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:47:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:47:25 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:48:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:48:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:48:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:48:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:48:57 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:49:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:49:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:49:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:49:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:49:07 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:49:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:49:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:49:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:49:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:49:26 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:50:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:50:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:50:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:50:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:50:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:51:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:51:07 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:51:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:51:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:51:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:51:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:51:27 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:52:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:52:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:52:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:52:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:52:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:53:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:53:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:53:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:53:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:53:08 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:53:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:53:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:53:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:53:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:53:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:54:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:54:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:54:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:55:00 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:55:00 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:55:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:55:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:55:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:55:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:55:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:55:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:55:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:55:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:55:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:55:29 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:56:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:56:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:56:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:57:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:57:01 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:57:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:57:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:57:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:57:10 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:57:10 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:57:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:57:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:57:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:57:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:57:30 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 15:59:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:59:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:59:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:59:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:59:02 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 15:59:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:59:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:59:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:59:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:59:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:59:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:59:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:59:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:59:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:59:31 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:01:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:01:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:01:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:01:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:01:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:01:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:01:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:01:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:01:12 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:01:12 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:01:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:01:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:01:32 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:03:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:03:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:03:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:03:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:03:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:03:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:03:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:03:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:03:13 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:03:13 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:03:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:03:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:03:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:03:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:03:33 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:05:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:05:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:05:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:05:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:05:05 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:05:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:05:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:05:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:05:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:05:14 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:05:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:05:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:05:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:05:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:05:34 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:07:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:07:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:07:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:07:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:07:06 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:07:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:07:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:07:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:07:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:07:15 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:07:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:07:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:07:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:07:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:07:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:09:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:09:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:09:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:09:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:09:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:09:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:09:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:09:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:09:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:09:16 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:09:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:09:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:09:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:09:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:09:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:11:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:11:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:11:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:11:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:11:08 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:11:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:11:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:11:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:11:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:11:17 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:11:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:11:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:11:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:11:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:11:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:13:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:13:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:13:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:13:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:13:09 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:13:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:13:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:13:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:13:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:13:18 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:13:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:13:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:13:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:13:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:13:38 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:15:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:15:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:15:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:15:10 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:15:10 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:15:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:15:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:15:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:15:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:15:19 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:15:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:15:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:15:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:15:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:15:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:17:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:17:11 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:17:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:17:20 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:17:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:17:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:17:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:17:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:17:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:19:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:19:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:19:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:19:12 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:19:12 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:19:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:19:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:19:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:19:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:19:21 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:19:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:19:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:19:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:19:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:19:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:21:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:21:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:21:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:21:13 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:21:13 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:21:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:21:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:21:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:21:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:21:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:21:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:21:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:21:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:21:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:21:42 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:23:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:23:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:23:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:23:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:23:14 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:23:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:23:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:23:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:23:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:23:23 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:23:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:23:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:23:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:23:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:23:43 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:25:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:25:15 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:25:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:25:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:25:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:25:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:25:24 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:25:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:25:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:25:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:25:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:25:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:27:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:27:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:27:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:27:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:27:16 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:27:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:27:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:27:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:27:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:27:24 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:27:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:27:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:27:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:27:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:27:45 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:29:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:29:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:29:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:29:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:29:17 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:29:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:29:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:29:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:29:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:29:25 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:29:46 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:29:46 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:31:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:31:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:31:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:31:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:31:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:31:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:31:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:31:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:31:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:31:26 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:31:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:31:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:31:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:31:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:31:47 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:33:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:33:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:33:19 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:33:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:33:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:33:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:33:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:33:27 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:33:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:33:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:33:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:33:48 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:33:48 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:35:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:35:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:35:20 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:35:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:35:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:35:28 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:35:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:35:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:35:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:35:49 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:35:49 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:37:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:37:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:37:21 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:37:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:37:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:37:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:37:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:37:29 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:37:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:37:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:37:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:37:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:37:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:39:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:39:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:39:22 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:39:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:39:30 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:39:30 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:39:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:39:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:39:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:39:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:39:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:41:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:41:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:41:23 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:41:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:41:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:41:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:41:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:41:31 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:41:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:41:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:41:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:41:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:41:53 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:43:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:43:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:43:24 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:43:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:43:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:43:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:43:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:43:32 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:43:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:43:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:43:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:43:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:43:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

[13-Aug-2025 16:44:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:21 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:21 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:44:24 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:44:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:26 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:26 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:26 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SELECT * FROM wp_woo_hide_shipping_rules ORDER BY created_at DESC made by do_action('woocommerce_page_woo-hide-shipping'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->admin_page, WooHideShipping_Admin->render_rules_list, WooHideShipping_Database::get_rules
[13-Aug-2025 16:44:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:29 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:44:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:44:48 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:44:48 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:44:48 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:44:48 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => Test
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => free_shipping
        )

    [status] => active
)

[13-Aug-2025 16:44:48 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SHOW FULL COLUMNS FROM `wp_woo_hide_shipping_rules` made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->handle_form_submission, WooHideShipping_Admin->handle_add_rule, WooHideShipping_Database::insert_rule
[13-Aug-2025 16:45:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:12 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:12 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:18 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:18 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:24 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:45:33 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:45:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:33 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SELECT * FROM wp_woo_hide_shipping_rules ORDER BY created_at DESC made by do_action('woocommerce_page_woo-hide-shipping'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->admin_page, WooHideShipping_Admin->render_rules_list, WooHideShipping_Database::get_rules
[13-Aug-2025 16:45:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:36 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:36 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:43 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:45:43 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:45:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:45:43 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => test
    [payment_methods] => Array
        (
            [0] => cod
        )

    [shipping_methods] => Array
        (
            [0] => free_shipping
        )

    [status] => active
)

[13-Aug-2025 16:45:43 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SHOW FULL COLUMNS FROM `wp_woo_hide_shipping_rules` made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->handle_form_submission, WooHideShipping_Admin->handle_add_rule, WooHideShipping_Database::insert_rule
[13-Aug-2025 16:45:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:45:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:45:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:45:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:45:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:46:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:46:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:46:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:10 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:46:10 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:11 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:12 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:46:12 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:46:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:46:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:46:25 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:47:12 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:12 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:12 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:14 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:14 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => true
)

[13-Aug-2025 16:47:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:20 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SELECT * FROM wp_woo_hide_shipping_rules ORDER BY created_at DESC made by do_action('woocommerce_page_woo-hide-shipping'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->admin_page, WooHideShipping_Admin->render_rules_list, WooHideShipping_Database::get_rules
[13-Aug-2025 16:47:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:22 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:22 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:32 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:47:32 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:47:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:32 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => testst
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:47:32 UTC] WordPress database error Table 'test.wp_woo_hide_shipping_rules' doesn't exist for query SHOW FULL COLUMNS FROM `wp_woo_hide_shipping_rules` made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, WooHideShipping_Admin->handle_form_submission, WooHideShipping_Admin->handle_add_rule, WooHideShipping_Database::insert_rule
[13-Aug-2025 16:47:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:34 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:47:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:47:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:47:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:47:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:47:51 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:48:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:48:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:48:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:48:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:48:26 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:48:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:48:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:48:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:48:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:48:27 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 16:48:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:48:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:48:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:48:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:48:34 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:49:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:49:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:49:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:49:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:49:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:49:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:49:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:49:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:49:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:49:35 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:49:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:49:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:49:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:49:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:49:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:50:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 16:50:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:27 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:27 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:50:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:32 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 16:50:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:50:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:50:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:39 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:39 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Creating table: wp_woo_hide_shipping_rules
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - SQL: CREATE TABLE wp_woo_hide_shipping_rules (
            id int(11) NOT NULL AUTO_INCREMENT,
            rule_name varchar(255) NOT NULL,
            payment_methods longtext NOT NULL,
            shipping_methods longtext NOT NULL,
            status enum('active','inactive') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - dbDelta result: Array
(
    [wp_woo_hide_shipping_rules] => Created table wp_woo_hide_shipping_rules
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Table exists after creation: YES
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Table name: wp_woo_hide_shipping_rules
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Original data: Array
(
    [rule_name] => Test Rule
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Serialized data: Array
(
    [rule_name] => Test Rule
    [payment_methods] => a:1:{i:0;s:4:"bacs";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Insert data: Array
(
    [rule_name] => Test Rule
    [payment_methods] => a:1:{i:0;s:4:"bacs";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - wpdb->insert result: 1
[13-Aug-2025 16:50:39 UTC] WooHideShipping Debug - Last query: INSERT INTO `wp_woo_hide_shipping_rules` (`rule_name`, `payment_methods`, `shipping_methods`, `status`) VALUES ('Test Rule', 'a:1:{i:0;s:4:\"bacs\";}', 'a:1:{i:0;s:9:\"flat_rate\";}', 'active')
[13-Aug-2025 16:50:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:49 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:49 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:50:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:50:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:50:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:50:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:50:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:50:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:06 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=add
    [action] => add_rule
    [rule_name] => papapap
    [payment_methods] => Array
        (
            [0] => cheque
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Rule data: Array
(
    [rule_name] => papapap
    [payment_methods] => Array
        (
            [0] => cheque
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Table name: wp_woo_hide_shipping_rules
[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Original data: Array
(
    [rule_name] => papapap
    [payment_methods] => Array
        (
            [0] => cheque
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Serialized data: Array
(
    [rule_name] => papapap
    [payment_methods] => a:1:{i:0;s:6:"cheque";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Insert data: Array
(
    [rule_name] => papapap
    [payment_methods] => a:1:{i:0;s:6:"cheque";}
    [shipping_methods] => a:1:{i:0;s:9:"flat_rate";}
    [status] => active
)

[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - wpdb->insert result: 1
[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Last query: INSERT INTO `wp_woo_hide_shipping_rules` (`rule_name`, `payment_methods`, `shipping_methods`, `status`) VALUES ('papapap', 'a:1:{i:0;s:6:\"cheque\";}', 'a:1:{i:0;s:9:\"flat_rate\";}', 'active')
[13-Aug-2025 16:51:06 UTC] WooHideShipping Debug - Rule inserted successfully with ID: 2
[13-Aug-2025 16:51:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:10 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:10 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:22 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:22 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:22 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping&action=edit&rule_id=2
    [action] => edit_rule
    [rule_id] => 2
    [rule_name] => papapap2121212
    [payment_methods] => Array
        (
            [0] => bacs
        )

    [shipping_methods] => Array
        (
            [0] => flat_rate
        )

    [status] => active
)

[13-Aug-2025 16:51:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:25 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:25 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:30 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:30 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:30 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:31 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:51:31 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:51:31 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:31 UTC] WAA: POST data: Array
(
    [woo_hide_shipping_nonce] => 99c12a9957
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-hide-shipping
    [action] => delete_rule
    [rule_id] => 2
)

[13-Aug-2025 16:51:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:36 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:52 UTC] WAA: POST data: Array
(
    [action] => wpaj_add_accessories_to_cart
    [product_id] => 381
    [quantity] => 1
    [nonce] => 1842142e2e
)

[13-Aug-2025 16:51:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:52 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:51:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:51:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:51:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:51:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:51:56 UTC] WAA: POST data: Array
(
    [action] => wpaj_add_accessories_to_cart
    [product_id] => 381
    [quantity] => 1
    [nonce] => 1842142e2e
)

[13-Aug-2025 16:52:27 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:27 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:27 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:28 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:28 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:52:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:33 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:52:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:52:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:51 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => true
)

[13-Aug-2025 16:52:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:53 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:52:53 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:52:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:52:55 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098996:1
)

[13-Aug-2025 16:52:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:52:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:52:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:52:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:52:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:52:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 16:52:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 16:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:14 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:15 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:15 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [method_id] => free_shipping
    [zone_id] => 
)

[13-Aug-2025 16:53:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:18 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [instance_id] => 1
    [data] => Array
        (
            [woocommerce_free_shipping_title] => Spedizione gratuita
            [woocommerce_free_shipping_requires] => 
            [woocommerce_free_shipping_min_amount] => 0
            [instance_id] => 1
        )

)

[13-Aug-2025 16:53:18 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:18 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:18 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:19 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [changes] => Array
        (
            [zone_name] => italia
            [zone_locations] => Array
                (
                    [0] => country:IT
                )

        )

    [zone_id] => 1
)

[13-Aug-2025 16:53:22 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:22 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:22 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:23 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [method_id] => flat_rate
    [zone_id] => 1
)

[13-Aug-2025 16:53:26 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:26 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:26 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:26 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:26 UTC] WAA: POST data: Array
(
    [wc_shipping_zones_nonce] => d24c3e93e5
    [instance_id] => 2
    [data] => Array
        (
            [woocommerce_flat_rate_title] => Tariffa unica
            [woocommerce_flat_rate_tax_status] => taxable
            [woocommerce_flat_rate_cost] => 3
            [instance_id] => 2
        )

)

[13-Aug-2025 16:53:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:54 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:53:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:53:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:53:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:53:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:53:55 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 16:54:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:02 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 16:54:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:29 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-order-status-buttons
    [has_focus] => false
)

[13-Aug-2025 16:54:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:33 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:54:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:54:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:54:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:54:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:54:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:56:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:56:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:56:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:56:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:56:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 16:56:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:56:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:56:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:56:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:56:34 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:56:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:56:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:56:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:56:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:56:38 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:58:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:58:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:58:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:58:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:58:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 16:58:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:58:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:58:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:58:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:58:35 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 16:58:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 16:58:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 16:58:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 16:58:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 16:58:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:00:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:00:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:00:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:00:05 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:00:05 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:00:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:00:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:00:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:00:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:00:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:00:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:00:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:00:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:01:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:01:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:01:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => true
)

[13-Aug-2025 17:01:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:01:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:01:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:01:59 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 17:01:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 17:02:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:02:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:02:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:02:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:02:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:03:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:03:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:03:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:03:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:03:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:04:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:04:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:04:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:04:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:04:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:05:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:05:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:05:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:05:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:05:03 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:06:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:06:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:06:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:06:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:06:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:07:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:07:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:07:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:07:04 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:07:04 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:08:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:08:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:08:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:08:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:08:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => true
)

[13-Aug-2025 17:08:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:08:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:08:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:08:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:08:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:10:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:10:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:10:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:10:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:10:36 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:10:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:10:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:10:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:10:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:10:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:12:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:12:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:12:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:12:37 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:12:37 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:12:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:12:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:12:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:12:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:12:42 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:13:36 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:13:36 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:13:36 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:13:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:13:38 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:14:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:14:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:14:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:14:44 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:14:44 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:15:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:15:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:15:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:15:39 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:15:39 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:16:43 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:16:43 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:16:43 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:16:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:16:45 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:17:38 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:17:38 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:17:38 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:17:40 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:17:40 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:18:44 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:18:44 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:18:44 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:18:46 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:18:46 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:19:39 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:19:39 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:19:39 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:19:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:19:41 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

[13-Aug-2025 17:20:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:20:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:20:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:20:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:20:47 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_woo-hide-shipping
    [has_focus] => false
)

[13-Aug-2025 17:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 17:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 17:21:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 17:21:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 17:21:42 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => woocommerce_page_wc-settings
    [has_focus] => false
)

