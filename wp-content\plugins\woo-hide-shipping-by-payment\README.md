# WooCommerce Hide Shipping by Payment Method

A WordPress plugin that conditionally hides shipping methods based on selected payment methods during WooCommerce checkout.

## Description

This plugin allows you to create rules that automatically hide specific shipping methods when customers select certain payment methods during checkout. This is useful for scenarios where certain payment methods are incompatible with specific shipping options.

## Features

- **Admin Interface**: Easy-to-use admin panel under WooCommerce menu
- **Rule Management**: Create, edit, and delete shipping visibility rules
- **Multi-select Options**: Choose multiple payment methods and shipping methods per rule
- **Real-time Updates**: Shipping methods are hidden/shown instantly via AJAX
- **Status Control**: Enable/disable rules without deleting them
- **Security**: Proper nonce validation and user permission checks
- **Internationalization**: Ready for translation (Italian included)

## Installation

1. Upload the plugin files to `/wp-content/plugins/woo-hide-shipping-by-payment/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Navigate to WooCommerce > Nascondi Spedizioni to configure rules

## Requirements

- WordPress 5.0 or higher
- WooCommerce 5.0 or higher
- PHP 7.4 or higher

## Usage

### Creating a Rule

1. Go to **WooCommerce > Nascondi Spedizioni**
2. Click **"Aggiungi Nuova Regola"**
3. Fill in the form:
   - **Rule Name**: A descriptive name for your rule
   - **Payment Methods**: Select payment methods that will trigger this rule
   - **Shipping Methods to Hide**: Select shipping methods to hide when the payment methods are selected
   - **Status**: Set to Active or Inactive
4. Click **"Salva Regola"**

### Managing Rules

- **Edit**: Click the "Modifica" button next to any rule
- **Delete**: Click the "Elimina" button (with confirmation)
- **Status**: Rules can be activated/deactivated without deletion

## How It Works

1. Customer selects a payment method during checkout
2. Plugin checks for active rules matching that payment method
3. Corresponding shipping methods are automatically hidden
4. Checkout updates in real-time without page refresh

## File Structure

```
woo-hide-shipping-by-payment/
├── woo-hide-shipping-by-payment.php    # Main plugin file
├── includes/
│   ├── class-database.php              # Database operations
│   ├── class-admin.php                 # Admin interface
│   ├── class-frontend.php              # Frontend functionality
│   └── class-ajax.php                  # AJAX handlers
├── assets/
│   ├── css/
│   │   └── admin.css                   # Admin styles
│   └── js/
│       ├── admin.js                    # Admin JavaScript
│       └── frontend.js                 # Frontend JavaScript
├── languages/
│   └── woo-hide-shipping-by-payment.pot # Translation template
└── README.md                           # This file
```

## Database Schema

The plugin creates a custom table `wp_woo_hide_shipping_rules` with the following structure:

- `id` (int): Primary key
- `rule_name` (varchar): Rule identifier
- `payment_methods` (longtext): Serialized array of payment method IDs
- `shipping_methods` (longtext): Serialized array of shipping method IDs
- `status` (enum): 'active' or 'inactive'
- `created_at` (datetime): Creation timestamp
- `updated_at` (datetime): Last update timestamp

## Hooks and Filters

### Actions Used
- `woocommerce_package_rates`: Filter shipping methods
- `woocommerce_checkout_update_order_review`: Handle payment method changes
- `wp_enqueue_scripts`: Load frontend assets
- `admin_enqueue_scripts`: Load admin assets

### AJAX Actions
- `woo_hide_shipping_delete_rule`: Delete a rule
- `woo_hide_shipping_toggle_status`: Toggle rule status
- `woo_hide_shipping_update_checkout`: Update checkout on payment change

## Security Features

- Nonce validation for all forms and AJAX requests
- User capability checks (`manage_woocommerce`)
- Input sanitization and validation
- SQL injection prevention with prepared statements

## Internationalization

The plugin is translation-ready with:
- Text domain: `woo-hide-shipping-by-payment`
- POT file included for translators
- Italian translations included

## Troubleshooting

### Shipping methods not hiding
1. Check that WooCommerce is active
2. Verify the rule is set to "Active" status
3. Ensure payment and shipping methods are correctly selected
4. Check browser console for JavaScript errors

### Admin interface not loading
1. Verify user has `manage_woocommerce` capability
2. Check for plugin conflicts
3. Ensure WooCommerce is installed and active

## Changelog

### 1.0.0
- Initial release
- Basic rule management functionality
- Real-time shipping method hiding
- Admin interface with CRUD operations
- Security and validation implementation

## Support

For support and feature requests, please contact the plugin developer.

## License

This plugin is licensed under the GPL v2 or later.