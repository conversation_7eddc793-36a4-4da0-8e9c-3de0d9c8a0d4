<?php
if (!defined('ABSPATH')) {
    exit;
}

// Assicurati che la variabile $buttons sia disponibile
if (!isset($buttons)) {
    $buttons = array();
}
?>

<table class="wp-list-table widefat fixed striped">
    <thead>
        <tr>
            <th scope="col" class="manage-column column-name">Nome Pulsante</th>
            <th scope="col" class="manage-column column-description">Descrizione</th>
            <th scope="col" class="manage-column column-order-statuses">Status Ordini</th>
            <th scope="col" class="manage-column column-target-status">Status Destinazione</th>
            <th scope="col" class="manage-column column-email-subject">Oggetto Email</th>
            <th scope="col" class="manage-column column-icon">Icona</th>
            <th scope="col" class="manage-column column-created">Data Creazione</th>
            <th scope="col" class="manage-column column-actions">Azioni</th>
        </tr>
    </thead>
    <tbody>
        <?php if (empty($buttons)): ?>
            <tr>
                <td colspan="8" style="text-align: center; padding: 20px;">
                    <p>Nessun pulsante configurato.</p>
                    <a href="<?php echo admin_url('admin.php?page=woo-order-status-buttons&action=add'); ?>" class="button button-primary">
                        Aggiungi il primo pulsante
                    </a>
                </td>
            </tr>
        <?php else: ?>
            <?php foreach ($buttons as $button): ?>
                <tr>
                    <td class="column-name">
                        <strong><?php echo esc_html($button->button_name); ?></strong>
                    </td>
                    
                    <td class="column-description">
                        <?php 
                        $description = !empty($button->button_description) ? $button->button_description : '—';
                        echo esc_html(wp_trim_words($description, 10));
                        ?>
                    </td>
                    
                    <td class="column-order-statuses">
                        <?php 
                        if (is_array($button->order_statuses) && !empty($button->order_statuses)) {
                            $status_names = array();
                            foreach ($button->order_statuses as $status) {
                                $status_names[] = wosb_get_status_name($status);
                            }
                            echo '<span class="wosb-status-list">' . esc_html(implode(', ', $status_names)) . '</span>';
                        } else {
                            echo '—';
                        }
                        ?>
                    </td>
                    
                    <td class="column-target-status">
                        <span class="wosb-target-status">
                            <?php echo esc_html(wosb_get_status_name($button->target_status)); ?>
                        </span>
                    </td>

                    <td class="column-email-subject">
                        <?php
                        $email_subject = !empty($button->email_subject) ? $button->email_subject : 'Automatico';
                        echo '<span class="wosb-email-subject">' . esc_html(wp_trim_words($email_subject, 8)) . '</span>';
                        ?>
                    </td>

                    <td class="column-icon">
                        <?php
                        // Genera CSS inline per l'icona
                        $unicode_input = !empty($button->dashicon_code) ? $button->dashicon_code : '\f147';

                        // Gestione dell'unicode come nel plugin esistente
                        if (preg_match('/^\\\\{1,2}f[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                            // Rimuovi tutti i backslash iniziali per ottenere il codice pulito
                            $unicode = preg_replace('/^\\\\+/', '', $unicode_input);
                        } else if (preg_match('/^f[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                            // Se inizia con 'f' ma senza backslash
                            $unicode = $unicode_input;
                        } else if (preg_match('/^[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                            // Se è solo il codice hex senza 'f', aggiungilo
                            $unicode = 'f' . $unicode_input;
                        } else {
                            // Fallback
                            $unicode = 'f147';
                        }
                        ?>
                        <span class="dashicons wosb-preview-icon" style="font-family: dashicons; font-size: 20px;">
                            <style>
                                .wosb-preview-icon-<?php echo $button->id; ?>::before {
                                    content: '\<?php echo $unicode; ?>' !important;
                                }
                            </style>
                            <span class="wosb-preview-icon-<?php echo $button->id; ?>"></span>
                        </span>
                        <code><?php echo esc_html($button->dashicon_code); ?></code>
                    </td>
                    
                    <td class="column-created">
                        <?php 
                        $created_date = new DateTime($button->created_at);
                        echo esc_html($created_date->format('d/m/Y H:i'));
                        ?>
                    </td>
                    
                    <td class="column-actions">
                        <div class="row-actions">
                            <span class="edit">
                                <a href="<?php echo admin_url('admin.php?page=woo-order-status-buttons&action=edit&button_id=' . $button->id); ?>">
                                    Modifica
                                </a>
                            </span>
                            
                            <span class="trash"> | 
                                <a href="<?php echo wp_nonce_url(
                                    admin_url('admin-post.php?action=wosb_delete_button&button_id=' . $button->id),
                                    'wosb_delete_button',
                                    'wosb_nonce'
                                ); ?>" 
                                class="submitdelete" 
                                onclick="return confirm('Sei sicuro di voler eliminare questo pulsante? Questa azione non può essere annullata.');">
                                    Elimina
                                </a>
                            </span>
                            
                            <span class="view"> | 
                                <a href="#" class="wosb-preview-email" data-button-id="<?php echo $button->id; ?>">
                                    Anteprima Email
                                </a>
                            </span>
                        </div>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php endif; ?>
    </tbody>
</table>

<?php if (!empty($buttons)): ?>
<div class="tablenav bottom">
    <div class="alignleft actions">
        <a href="<?php echo admin_url('admin.php?page=woo-order-status-buttons&action=add'); ?>" class="button button-primary">
            Aggiungi Nuovo Pulsante
        </a>
    </div>
    
    <div class="alignright">
        <span class="displaying-num">
            <?php printf(_n('%s elemento', '%s elementi', count($buttons)), count($buttons)); ?>
        </span>
    </div>
</div>
<?php endif; ?>

<style>
.wosb-status-list {
    font-size: 12px;
    line-height: 1.4;
}

.wosb-target-status {
    display: inline-block;
    padding: 2px 8px;
    background: #0073aa;
    color: white;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.wosb-preview-icon {
    margin-right: 5px;
    color: #0073aa;
}

.wosb-email-subject {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

.column-name {
    width: 15%;
}

.column-description {
    width: 20%;
}

.column-order-statuses {
    width: 15%;
}

.column-target-status {
    width: 12%;
}

.column-email-subject {
    width: 15%;
}

.column-icon {
    width: 8%;
}

.column-created {
    width: 8%;
}

.column-actions {
    width: 12%;
}

@media (max-width: 782px) {
    .column-description,
    .column-order-statuses,
    .column-email-subject,
    .column-icon,
    .column-created {
        display: none;
    }

    .column-name {
        width: 50%;
    }

    .column-target-status {
        width: 25%;
    }

    .column-actions {
        width: 25%;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    $('.wosb-preview-email').on('click', function(e) {
        e.preventDefault();
        
        var buttonId = $(this).data('button-id');
        
        // Per ora mostra un alert, in futuro si può implementare un modal
        alert('Funzionalità anteprima email in sviluppo per il pulsante ID: ' + buttonId);
    });
});
</script>
