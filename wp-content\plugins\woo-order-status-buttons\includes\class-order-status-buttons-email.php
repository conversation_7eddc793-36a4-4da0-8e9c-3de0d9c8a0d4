<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione delle email
 */
class Order_Status_Buttons_Email {
    
    public function __construct() {
        // Hook per personalizzare le email WooCommerce se necessario
        add_filter('woocommerce_email_classes', array($this, 'add_custom_email_class'));
    }
    
    /**
     * Invia email personalizzata al cliente
     */
    public function send_custom_email($order, $button) {
        if (!$order || !$button) {
            wosb_log('Ordine o pulsante non valido per l\'invio email', 'error');
            return false;
        }
        
        // Ottieni l'email del cliente
        $customer_email = $order->get_billing_email();
        
        if (empty($customer_email)) {
            wosb_log('Email cliente non trovata per l\'ordine #' . $order->get_id(), 'error');
            return false;
        }
        
        // Prepara il contenuto dell'email
        $email_content = $this->prepare_email_content($order, $button);
        $subject = $this->prepare_email_subject($order, $button);
        
        // Usa il sistema email di WooCommerce
        $mailer = WC()->mailer();
        
        // Ottieni il template email di WooCommerce
        $email_heading = $subject;
        $email_body = $this->wrap_email_content($email_content, $order, $email_heading);
        
        // Invia l'email
        $sent = $mailer->send(
            $customer_email,
            $subject,
            $email_body,
            array('Content-Type: text/html; charset=UTF-8')
        );
        
        if ($sent) {
            // Aggiungi nota all'ordine
            $order->add_order_note(
                sprintf(
                    'Email personalizzata inviata al cliente tramite pulsante "%s". Destinatario: %s',
                    $button->button_name,
                    $customer_email
                ),
                false,
                true
            );
            
            wosb_log('Email inviata con successo per l\'ordine #' . $order->get_id(), 'info');
            return true;
        } else {
            wosb_log('Errore nell\'invio email per l\'ordine #' . $order->get_id(), 'error');
            return false;
        }
    }
    
    /**
     * Prepara il contenuto dell'email sostituendo i placeholder
     */
    private function prepare_email_content($order, $button) {
        $content = $button->email_content;
        
        // Placeholder disponibili
        $placeholders = array(
            '{order_number}' => $order->get_order_number(),
            '{order_id}' => $order->get_id(),
            '{customer_name}' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            '{customer_first_name}' => $order->get_billing_first_name(),
            '{customer_last_name}' => $order->get_billing_last_name(),
            '{order_date}' => wc_format_datetime($order->get_date_created()),
            '{order_total}' => $order->get_formatted_order_total(),
            '{order_status}' => wc_get_order_status_name($order->get_status()),
            '{new_status}' => wosb_get_status_name($button->target_status),
            '{site_name}' => get_bloginfo('name'),
            '{site_url}' => home_url(),
            '{order_details}' => $this->get_order_details_html($order),
            '{billing_address}' => $order->get_formatted_billing_address(),
            '{shipping_address}' => $order->get_formatted_shipping_address(),
        );
        
        // Sostituisci i placeholder
        foreach ($placeholders as $placeholder => $value) {
            $content = str_replace($placeholder, $value, $content);
        }
        
        return $content;
    }
    
    /**
     * Prepara l'oggetto dell'email
     */
    private function prepare_email_subject($order, $button) {
        // Usa l'oggetto personalizzato se presente, altrimenti genera automaticamente
        if (!empty($button->email_subject)) {
            $subject = $button->email_subject;

            // Sostituisci i placeholder nell'oggetto
            $placeholders = array(
                '{order_number}' => $order->get_order_number(),
                '{order_id}' => $order->get_id(),
                '{customer_name}' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
                '{customer_first_name}' => $order->get_billing_first_name(),
                '{customer_last_name}' => $order->get_billing_last_name(),
                '{order_date}' => wc_format_datetime($order->get_date_created()),
                '{order_total}' => $order->get_formatted_order_total(),
                '{order_status}' => wc_get_order_status_name($order->get_status()),
                '{new_status}' => wosb_get_status_name($button->target_status),
                '{site_name}' => get_bloginfo('name'),
                '{site_url}' => home_url(),
            );

            // Sostituisci i placeholder
            foreach ($placeholders as $placeholder => $value) {
                $subject = str_replace($placeholder, $value, $subject);
            }
        } else {
            // Oggetto di default
            $subject = sprintf(
                'Aggiornamento ordine #%s - %s',
                $order->get_order_number(),
                $button->button_name
            );
        }

        return apply_filters('wosb_email_subject', $subject, $order, $button);
    }
    
    /**
     * Avvolge il contenuto email nel template WooCommerce
     */
    private function wrap_email_content($content, $order, $heading) {
        // Ottieni il template email di WooCommerce
        ob_start();
        
        // Header email
        wc_get_template('emails/email-header.php', array(
            'email_heading' => $heading
        ));
        
        // Contenuto personalizzato
        echo '<div style="margin-bottom: 40px;">';
        echo wpautop($content);
        echo '</div>';
        
        // Footer email
        wc_get_template('emails/email-footer.php');
        
        return ob_get_clean();
    }
    
    /**
     * Ottieni i dettagli dell'ordine in formato HTML
     */
    private function get_order_details_html($order) {
        ob_start();
        
        echo '<h3>Dettagli Ordine</h3>';
        echo '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        echo '<thead>';
        echo '<tr style="background-color: #f8f8f8;">';
        echo '<th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Prodotto</th>';
        echo '<th style="padding: 10px; border: 1px solid #ddd; text-align: center;">Quantità</th>';
        echo '<th style="padding: 10px; border: 1px solid #ddd; text-align: right;">Prezzo</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            $product_name = $item->get_name();
            $quantity = $item->get_quantity();
            $total = $order->get_formatted_line_subtotal($item);
            
            echo '<tr>';
            echo '<td style="padding: 10px; border: 1px solid #ddd;">' . esc_html($product_name) . '</td>';
            echo '<td style="padding: 10px; border: 1px solid #ddd; text-align: center;">' . esc_html($quantity) . '</td>';
            echo '<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">' . $total . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        
        // Totali ordine
        echo '<table style="width: 100%; border-collapse: collapse;">';
        
        foreach ($order->get_order_item_totals() as $key => $total) {
            echo '<tr>';
            echo '<td style="padding: 5px; text-align: right; font-weight: bold;">' . $total['label'] . '</td>';
            echo '<td style="padding: 5px; text-align: right;">' . $total['value'] . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        
        return ob_get_clean();
    }
    
    /**
     * Aggiunge classe email personalizzata (se necessario in futuro)
     */
    public function add_custom_email_class($email_classes) {
        // Placeholder per future email personalizzate
        return $email_classes;
    }
    
    /**
     * Testa l'invio di un'email (per debug)
     */
    public function test_email($order_id, $button_id) {
        if (!wosb_user_can_manage()) {
            return false;
        }
        
        $order = wc_get_order($order_id);
        $db = new Order_Status_Buttons_DB();
        $button = $db->get_button($button_id);
        
        if (!$order || !$button) {
            return false;
        }
        
        return $this->send_custom_email($order, $button);
    }
    
    /**
     * Ottieni anteprima email
     */
    public function get_email_preview($order_id, $button_id) {
        if (!wosb_user_can_manage()) {
            return false;
        }
        
        $order = wc_get_order($order_id);
        $db = new Order_Status_Buttons_DB();
        $button = $db->get_button($button_id);
        
        if (!$order || !$button) {
            return false;
        }
        
        $email_content = $this->prepare_email_content($order, $button);
        $subject = $this->prepare_email_subject($order, $button);
        $email_body = $this->wrap_email_content($email_content, $order, $subject);
        
        return array(
            'subject' => $subject,
            'body' => $email_body
        );
    }
    
    /**
     * Valida l'indirizzo email
     */
    private function validate_email($email) {
        return is_email($email);
    }
    
    /**
     * Log delle email inviate
     */
    private function log_email_sent($order, $button, $recipient, $success) {
        $log_message = sprintf(
            'Email %s per ordine #%s, pulsante "%s", destinatario: %s',
            $success ? 'inviata' : 'fallita',
            $order->get_id(),
            $button->button_name,
            $recipient
        );
        
        wosb_log($log_message, $success ? 'info' : 'error');
    }
}
