<?php
/**
 * Admin interface for WooCommerce Hide Shipping by Payment Method
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooHideShipping_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'handle_form_submission'));
    }

    /**
     * Add admin menu under WooCommerce
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Nascondi Spedizioni', 'woo-hide-shipping-by-payment'),
            __('Nascondi Spedizioni', 'woo-hide-shipping-by-payment'),
            'manage_woocommerce',
            'woo-hide-shipping',
            array($this, 'admin_page')
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'woocommerce_page_woo-hide-shipping') {
            return;
        }

        wp_enqueue_style(
            'woo-hide-shipping-admin',
            WOO_HIDE_SHIPPING_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WOO_HIDE_SHIPPING_VERSION
        );

        wp_enqueue_script(
            'woo-hide-shipping-admin',
            WOO_HIDE_SHIPPING_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WOO_HIDE_SHIPPING_VERSION,
            true
        );

        wp_localize_script('woo-hide-shipping-admin', 'wooHideShipping', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('woo_hide_shipping_nonce'),
            'confirm_delete' => __('Sei sicuro di voler eliminare questa regola?', 'woo-hide-shipping-by-payment')
        ));
    }

    /**
     * Handle form submissions
     */
    public function handle_form_submission() {
        if (!isset($_POST['woo_hide_shipping_nonce']) || !wp_verify_nonce($_POST['woo_hide_shipping_nonce'], 'woo_hide_shipping_action')) {
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            return;
        }

        $action = isset($_POST['action']) ? sanitize_text_field($_POST['action']) : '';

        switch ($action) {
            case 'add_rule':
                $this->handle_add_rule();
                break;
            case 'edit_rule':
                $this->handle_edit_rule();
                break;
            case 'delete_rule':
                $this->handle_delete_rule();
                break;
        }
    }

    /**
     * Handle adding a new rule
     */
    private function handle_add_rule() {
        $rule_name = sanitize_text_field($_POST['rule_name']);
        $payment_methods = isset($_POST['payment_methods']) ? array_map('sanitize_text_field', $_POST['payment_methods']) : array();
        $shipping_methods = isset($_POST['shipping_methods']) ? array_map('sanitize_text_field', $_POST['shipping_methods']) : array();
        $status = sanitize_text_field($_POST['status']);

        if (empty($rule_name) || empty($payment_methods) || empty($shipping_methods)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Tutti i campi sono obbligatori.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
            return;
        }

        $data = array(
            'rule_name' => $rule_name,
            'payment_methods' => $payment_methods,
            'shipping_methods' => $shipping_methods,
            'status' => $status
        );

        $result = WooHideShipping_Database::insert_rule($data);

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Regola aggiunta con successo.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Errore durante l\'aggiunta della regola.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
        }
    }

    /**
     * Handle editing a rule
     */
    private function handle_edit_rule() {
        $rule_id = intval($_POST['rule_id']);
        $rule_name = sanitize_text_field($_POST['rule_name']);
        $payment_methods = isset($_POST['payment_methods']) ? array_map('sanitize_text_field', $_POST['payment_methods']) : array();
        $shipping_methods = isset($_POST['shipping_methods']) ? array_map('sanitize_text_field', $_POST['shipping_methods']) : array();
        $status = sanitize_text_field($_POST['status']);

        if (empty($rule_name) || empty($payment_methods) || empty($shipping_methods)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Tutti i campi sono obbligatori.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
            return;
        }

        $data = array(
            'rule_name' => $rule_name,
            'payment_methods' => $payment_methods,
            'shipping_methods' => $shipping_methods,
            'status' => $status
        );

        $result = WooHideShipping_Database::update_rule($rule_id, $data);

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Regola aggiornata con successo.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Errore durante l\'aggiornamento della regola.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
        }
    }

    /**
     * Handle deleting a rule
     */
    private function handle_delete_rule() {
        $rule_id = intval($_POST['rule_id']);

        $result = WooHideShipping_Database::delete_rule($rule_id);

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Regola eliminata con successo.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Errore durante l\'eliminazione della regola.', 'woo-hide-shipping-by-payment') . '</p></div>';
            });
        }
    }

    /**
     * Main admin page
     */
    public function admin_page() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
        $rule_id = isset($_GET['rule_id']) ? intval($_GET['rule_id']) : 0;

        ?>
        <div class="wrap">
            <h1><?php _e('Nascondi Spedizioni', 'woo-hide-shipping-by-payment'); ?></h1>

            <?php
            switch ($action) {
                case 'add':
                    $this->render_add_rule_form();
                    break;
                case 'edit':
                    $this->render_edit_rule_form($rule_id);
                    break;
                default:
                    $this->render_rules_list();
                    break;
            }
            ?>
        </div>
        <?php
    }

    /**
     * Render rules list
     */
    private function render_rules_list() {
        $rules = WooHideShipping_Database::get_rules('all');
        ?>
        <div class="woo-hide-shipping-header">
            <a href="<?php echo admin_url('admin.php?page=woo-hide-shipping&action=add'); ?>" class="button button-primary">
                <?php _e('Aggiungi Nuova Regola', 'woo-hide-shipping-by-payment'); ?>
            </a>
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Nome Regola', 'woo-hide-shipping-by-payment'); ?></th>
                    <th><?php _e('Metodi di Pagamento', 'woo-hide-shipping-by-payment'); ?></th>
                    <th><?php _e('Metodi di Spedizione da Nascondere', 'woo-hide-shipping-by-payment'); ?></th>
                    <th><?php _e('Stato', 'woo-hide-shipping-by-payment'); ?></th>
                    <th><?php _e('Azioni', 'woo-hide-shipping-by-payment'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if ($rules): ?>
                    <?php foreach ($rules as $rule): ?>
                        <tr>
                            <td><strong><?php echo esc_html($rule->rule_name); ?></strong></td>
                            <td><?php echo $this->format_methods_list($rule->payment_methods, 'payment'); ?></td>
                            <td><?php echo $this->format_methods_list($rule->shipping_methods, 'shipping'); ?></td>
                            <td>
                                <span class="status-<?php echo esc_attr($rule->status); ?>">
                                    <?php echo $rule->status === 'active' ? __('Attiva', 'woo-hide-shipping-by-payment') : __('Inattiva', 'woo-hide-shipping-by-payment'); ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=woo-hide-shipping&action=edit&rule_id=' . $rule->id); ?>" class="button button-small">
                                    <?php _e('Modifica', 'woo-hide-shipping-by-payment'); ?>
                                </a>
                                <form method="post" style="display: inline;">
                                    <?php wp_nonce_field('woo_hide_shipping_action', 'woo_hide_shipping_nonce'); ?>
                                    <input type="hidden" name="action" value="delete_rule">
                                    <input type="hidden" name="rule_id" value="<?php echo $rule->id; ?>">
                                    <button type="submit" class="button button-small button-link-delete" onclick="return confirm('<?php echo esc_js(__('Sei sicuro di voler eliminare questa regola?', 'woo-hide-shipping-by-payment')); ?>')">
                                        <?php _e('Elimina', 'woo-hide-shipping-by-payment'); ?>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="5"><?php _e('Nessuna regola trovata.', 'woo-hide-shipping-by-payment'); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Format methods list for display
     */
    private function format_methods_list($methods, $type) {
        if (!is_array($methods) || empty($methods)) {
            return '-';
        }

        $formatted = array();

        if ($type === 'payment') {
            $available_methods = $this->get_payment_methods();
            foreach ($methods as $method) {
                $formatted[] = isset($available_methods[$method]) ? $available_methods[$method] : $method;
            }
        } else {
            $available_methods = $this->get_shipping_methods();
            foreach ($methods as $method) {
                $formatted[] = isset($available_methods[$method]) ? $available_methods[$method] : $method;
            }
        }

        return implode(', ', $formatted);
    }

    /**
     * Get available payment methods
     */
    private function get_payment_methods() {
        $payment_gateways = WC()->payment_gateways->payment_gateways();
        $methods = array();

        foreach ($payment_gateways as $gateway) {
            if ($gateway->enabled === 'yes') {
                $methods[$gateway->id] = $gateway->get_title();
            }
        }

        return $methods;
    }

    /**
     * Get available shipping methods
     */
    private function get_shipping_methods() {
        $shipping_methods = WC()->shipping->get_shipping_methods();
        $methods = array();

        foreach ($shipping_methods as $method) {
            $methods[$method->id] = $method->get_method_title();
        }

        // Also get shipping zones and their methods
        $zones = WC_Shipping_Zones::get_zones();
        foreach ($zones as $zone) {
            $zone_obj = new WC_Shipping_Zone($zone['id']);
            $zone_methods = $zone_obj->get_shipping_methods();
            foreach ($zone_methods as $method) {
                $methods[$method->get_rate_id()] = $zone['zone_name'] . ' - ' . $method->get_title();
            }
        }

        return $methods;
    }

    /**
     * Render add rule form
     */
    private function render_add_rule_form() {
        ?>
        <h2><?php _e('Aggiungi Nuova Regola', 'woo-hide-shipping-by-payment'); ?></h2>

        <form method="post" class="woo-hide-shipping-form">
            <?php wp_nonce_field('woo_hide_shipping_action', 'woo_hide_shipping_nonce'); ?>
            <input type="hidden" name="action" value="add_rule">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="rule_name"><?php _e('Nome Regola', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="rule_name" name="rule_name" class="regular-text" required>
                        <p class="description"><?php _e('Inserisci un nome identificativo per questa regola.', 'woo-hide-shipping-by-payment'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="payment_methods"><?php _e('Metodi di Pagamento', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <select id="payment_methods" name="payment_methods[]" multiple class="woo-hide-shipping-select" required>
                            <?php foreach ($this->get_payment_methods() as $id => $title): ?>
                                <option value="<?php echo esc_attr($id); ?>"><?php echo esc_html($title); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php _e('Seleziona i metodi di pagamento che attiveranno questa regola.', 'woo-hide-shipping-by-payment'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="shipping_methods"><?php _e('Metodi di Spedizione da Nascondere', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <select id="shipping_methods" name="shipping_methods[]" multiple class="woo-hide-shipping-select" required>
                            <?php foreach ($this->get_shipping_methods() as $id => $title): ?>
                                <option value="<?php echo esc_attr($id); ?>"><?php echo esc_html($title); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php _e('Seleziona i metodi di spedizione da nascondere quando vengono selezionati i metodi di pagamento sopra.', 'woo-hide-shipping-by-payment'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="status"><?php _e('Stato', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <select id="status" name="status">
                            <option value="active"><?php _e('Attiva', 'woo-hide-shipping-by-payment'); ?></option>
                            <option value="inactive"><?php _e('Inattiva', 'woo-hide-shipping-by-payment'); ?></option>
                        </select>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" class="button button-primary" value="<?php _e('Salva Regola', 'woo-hide-shipping-by-payment'); ?>">
                <a href="<?php echo admin_url('admin.php?page=woo-hide-shipping'); ?>" class="button"><?php _e('Annulla', 'woo-hide-shipping-by-payment'); ?></a>
            </p>
        </form>
        <?php
    }

    /**
     * Render edit rule form
     */
    private function render_edit_rule_form($rule_id) {
        $rule = WooHideShipping_Database::get_rule($rule_id);

        if (!$rule) {
            echo '<div class="notice notice-error"><p>' . __('Regola non trovata.', 'woo-hide-shipping-by-payment') . '</p></div>';
            return;
        }

        ?>
        <h2><?php _e('Modifica Regola', 'woo-hide-shipping-by-payment'); ?></h2>

        <form method="post" class="woo-hide-shipping-form">
            <?php wp_nonce_field('woo_hide_shipping_action', 'woo_hide_shipping_nonce'); ?>
            <input type="hidden" name="action" value="edit_rule">
            <input type="hidden" name="rule_id" value="<?php echo $rule->id; ?>">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="rule_name"><?php _e('Nome Regola', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="rule_name" name="rule_name" class="regular-text" value="<?php echo esc_attr($rule->rule_name); ?>" required>
                        <p class="description"><?php _e('Inserisci un nome identificativo per questa regola.', 'woo-hide-shipping-by-payment'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="payment_methods"><?php _e('Metodi di Pagamento', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <select id="payment_methods" name="payment_methods[]" multiple class="woo-hide-shipping-select" required>
                            <?php foreach ($this->get_payment_methods() as $id => $title): ?>
                                <option value="<?php echo esc_attr($id); ?>" <?php selected(in_array($id, $rule->payment_methods)); ?>>
                                    <?php echo esc_html($title); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php _e('Seleziona i metodi di pagamento che attiveranno questa regola.', 'woo-hide-shipping-by-payment'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="shipping_methods"><?php _e('Metodi di Spedizione da Nascondere', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <select id="shipping_methods" name="shipping_methods[]" multiple class="woo-hide-shipping-select" required>
                            <?php foreach ($this->get_shipping_methods() as $id => $title): ?>
                                <option value="<?php echo esc_attr($id); ?>" <?php selected(in_array($id, $rule->shipping_methods)); ?>>
                                    <?php echo esc_html($title); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php _e('Seleziona i metodi di spedizione da nascondere quando vengono selezionati i metodi di pagamento sopra.', 'woo-hide-shipping-by-payment'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="status"><?php _e('Stato', 'woo-hide-shipping-by-payment'); ?></label>
                    </th>
                    <td>
                        <select id="status" name="status">
                            <option value="active" <?php selected($rule->status, 'active'); ?>><?php _e('Attiva', 'woo-hide-shipping-by-payment'); ?></option>
                            <option value="inactive" <?php selected($rule->status, 'inactive'); ?>><?php _e('Inattiva', 'woo-hide-shipping-by-payment'); ?></option>
                        </select>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" class="button button-primary" value="<?php _e('Aggiorna Regola', 'woo-hide-shipping-by-payment'); ?>">
                <a href="<?php echo admin_url('admin.php?page=woo-hide-shipping'); ?>" class="button"><?php _e('Annulla', 'woo-hide-shipping-by-payment'); ?></a>
            </p>
        </form>
        <?php
    }
}