/**
 * Admin JavaScript for WooCommerce Hide Shipping by Payment Method
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize admin functionality
    var WooHideShippingAdmin = {

        init: function() {
            this.bindEvents();
            this.initMultiSelect();
        },

        bindEvents: function() {
            // AJAX delete rule
            $(document).on('click', '.woo-hide-shipping-delete-rule', this.deleteRule);

            // Toggle rule status
            $(document).on('click', '.woo-hide-shipping-toggle-status', this.toggleStatus);

            // Form validation
            $(document).on('submit', '.woo-hide-shipping-form', this.validateForm);

            // Multi-select helper
            $(document).on('change', '.woo-hide-shipping-select', this.updateSelectDisplay);
        },

        initMultiSelect: function() {
            // Enhance multi-select dropdowns
            $('.woo-hide-shipping-select').each(function() {
                var $select = $(this);
                var selectedCount = $select.find('option:selected').length;

                if (selectedCount > 0) {
                    $select.attr('title', selectedCount + ' elementi selezionati');
                }
            });
        },

        deleteRule: function(e) {
            e.preventDefault();

            var $button = $(this);
            var ruleId = $button.data('rule-id');

            if (!confirm(wooHideShipping.confirm_delete)) {
                return;
            }

            // Show loading state
            $button.prop('disabled', true).text('Eliminazione...');

            $.ajax({
                url: wooHideShipping.ajax_url,
                type: 'POST',
                data: {
                    action: 'woo_hide_shipping_delete_rule',
                    rule_id: ruleId,
                    nonce: wooHideShipping.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Remove the row from table
                        $button.closest('tr').fadeOut(300, function() {
                            $(this).remove();
                        });

                        // Show success message
                        WooHideShippingAdmin.showNotice(response.data, 'success');
                    } else {
                        WooHideShippingAdmin.showNotice(response.data, 'error');
                        $button.prop('disabled', false).text('Elimina');
                    }
                },
                error: function() {
                    WooHideShippingAdmin.showNotice('Errore durante la comunicazione con il server.', 'error');
                    $button.prop('disabled', false).text('Elimina');
                }
            });
        },

        toggleStatus: function(e) {
            e.preventDefault();

            var $button = $(this);
            var ruleId = $button.data('rule-id');
            var currentStatus = $button.data('current-status');
            var newStatus = currentStatus === 'active' ? 'inactive' : 'active';

            // Show loading state
            $button.prop('disabled', true);

            $.ajax({
                url: wooHideShipping.ajax_url,
                type: 'POST',
                data: {
                    action: 'woo_hide_shipping_toggle_status',
                    rule_id: ruleId,
                    status: newStatus,
                    nonce: wooHideShipping.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update button and status display
                        var $statusSpan = $button.closest('tr').find('.status-' + currentStatus);
                        $statusSpan.removeClass('status-' + currentStatus)
                                  .addClass('status-' + newStatus)
                                  .text(newStatus === 'active' ? 'Attiva' : 'Inattiva');

                        $button.data('current-status', newStatus)
                               .text(newStatus === 'active' ? 'Disattiva' : 'Attiva');

                        WooHideShippingAdmin.showNotice(response.data.message, 'success');
                    } else {
                        WooHideShippingAdmin.showNotice(response.data, 'error');
                    }

                    $button.prop('disabled', false);
                },
                error: function() {
                    WooHideShippingAdmin.showNotice('Errore durante la comunicazione con il server.', 'error');
                    $button.prop('disabled', false);
                }
            });
        },

        validateForm: function(e) {
            var isValid = true;
            var $form = $(this);

            // Clear previous errors
            $form.find('.field-error').remove();
            $form.find('.required-field').removeClass('required-field');

            // Validate rule name
            var $ruleName = $form.find('#rule_name');
            if (!$ruleName.val().trim()) {
                WooHideShippingAdmin.showFieldError($ruleName, 'Il nome della regola è obbligatorio.');
                isValid = false;
            }

            // Validate payment methods
            var $paymentMethods = $form.find('#payment_methods');
            if ($paymentMethods.find('option:selected').length === 0) {
                WooHideShippingAdmin.showFieldError($paymentMethods, 'Seleziona almeno un metodo di pagamento.');
                isValid = false;
            }

            // Validate shipping methods
            var $shippingMethods = $form.find('#shipping_methods');
            if ($shippingMethods.find('option:selected').length === 0) {
                WooHideShippingAdmin.showFieldError($shippingMethods, 'Seleziona almeno un metodo di spedizione.');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: $form.find('.required-field').first().offset().top - 100
                }, 500);
            }
        },

        showFieldError: function($field, message) {
            $field.addClass('required-field');
            $field.after('<span class="field-error">' + message + '</span>');
        },

        updateSelectDisplay: function() {
            var $select = $(this);
            var selectedCount = $select.find('option:selected').length;
            var totalCount = $select.find('option').length;

            if (selectedCount > 0) {
                $select.attr('title', selectedCount + ' di ' + totalCount + ' elementi selezionati');
            } else {
                $select.removeAttr('title');
            }
        },

        showNotice: function(message, type) {
            var noticeClass = 'notice-' + type;
            var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible woo-hide-shipping-notice"><p>' + message + '</p></div>');

            // Remove existing notices
            $('.woo-hide-shipping-notice').remove();

            // Add new notice
            $('.wrap h1').after($notice);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);

            // Scroll to notice
            $('html, body').animate({
                scrollTop: $notice.offset().top - 100
            }, 300);
        }
    };

    // Initialize when document is ready
    WooHideShippingAdmin.init();
});