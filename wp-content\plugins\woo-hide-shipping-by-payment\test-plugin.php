<?php
/**
 * Test script for WooCommerce Hide Shipping by Payment Method plugin
 *
 * This script can be run to verify the plugin structure and basic functionality
 * Run this from WordPress admin or via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Plugin Test Class
 */
class WooHideShippingTest {

    public static function run_tests() {
        echo "<h2>WooCommerce Hide Shipping by Payment Method - Plugin Tests</h2>";

        self::test_plugin_structure();
        self::test_constants();
        self::test_classes();
        self::test_database();
        self::test_hooks();

        echo "<h3>✅ All tests completed!</h3>";
    }

    private static function test_plugin_structure() {
        echo "<h3>Testing Plugin Structure</h3>";

        $required_files = [
            'woo-hide-shipping-by-payment.php',
            'includes/class-database.php',
            'includes/class-admin.php',
            'includes/class-frontend.php',
            'includes/class-ajax.php',
            'assets/css/admin.css',
            'assets/js/admin.js',
            'assets/js/frontend.js',
            'languages/woo-hide-shipping-by-payment.pot',
            'README.md'
        ];

        $plugin_dir = WOO_HIDE_SHIPPING_PLUGIN_DIR;

        foreach ($required_files as $file) {
            if (file_exists($plugin_dir . $file)) {
                echo "✅ {$file} exists<br>";
            } else {
                echo "❌ {$file} missing<br>";
            }
        }
    }

    private static function test_constants() {
        echo "<h3>Testing Plugin Constants</h3>";

        $constants = [
            'WOO_HIDE_SHIPPING_VERSION',
            'WOO_HIDE_SHIPPING_PLUGIN_FILE',
            'WOO_HIDE_SHIPPING_PLUGIN_DIR',
            'WOO_HIDE_SHIPPING_PLUGIN_URL',
            'WOO_HIDE_SHIPPING_PLUGIN_BASENAME'
        ];

        foreach ($constants as $constant) {
            if (defined($constant)) {
                echo "✅ {$constant}: " . constant($constant) . "<br>";
            } else {
                echo "❌ {$constant} not defined<br>";
            }
        }
    }

    private static function test_classes() {
        echo "<h3>Testing Plugin Classes</h3>";

        $classes = [
            'WooHideShippingByPayment',
            'WooHideShipping_Database',
            'WooHideShipping_Admin',
            'WooHideShipping_Frontend',
            'WooHideShipping_Ajax'
        ];

        foreach ($classes as $class) {
            if (class_exists($class)) {
                echo "✅ {$class} class loaded<br>";
            } else {
                echo "❌ {$class} class not found<br>";
            }
        }
    }

    private static function test_database() {
        echo "<h3>Testing Database</h3>";

        global $wpdb;
        $table_name = WooHideShippingByPayment::get_table_name();

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;

        if ($table_exists) {
            echo "✅ Database table {$table_name} exists<br>";

            // Check table structure
            $columns = $wpdb->get_results("DESCRIBE {$table_name}");
            echo "✅ Table has " . count($columns) . " columns<br>";

            foreach ($columns as $column) {
                echo "  - {$column->Field} ({$column->Type})<br>";
            }
        } else {
            echo "❌ Database table {$table_name} does not exist<br>";
            echo "💡 Try activating the plugin to create the table<br>";
        }
    }

    private static function test_hooks() {
        echo "<h3>Testing WordPress Hooks</h3>";

        // Check if WooCommerce is active
        if (class_exists('WooCommerce')) {
            echo "✅ WooCommerce is active<br>";
        } else {
            echo "❌ WooCommerce is not active<br>";
            return;
        }

        // Check admin menu hook
        if (has_action('admin_menu')) {
            echo "✅ Admin menu hooks registered<br>";
        }

        // Check frontend hooks
        if (has_filter('woocommerce_package_rates')) {
            echo "✅ Shipping filter hooks registered<br>";
        }

        // Check AJAX hooks
        $ajax_actions = [
            'wp_ajax_woo_hide_shipping_delete_rule',
            'wp_ajax_woo_hide_shipping_toggle_status',
            'wp_ajax_woo_hide_shipping_update_checkout'
        ];

        foreach ($ajax_actions as $action) {
            if (has_action($action)) {
                echo "✅ AJAX action {$action} registered<br>";
            } else {
                echo "❌ AJAX action {$action} not registered<br>";
            }
        }
    }
}

// Run tests if accessed directly (for debugging)
if (isset($_GET['run_woo_hide_shipping_tests']) && current_user_can('manage_options')) {
    WooHideShippingTest::run_tests();
}