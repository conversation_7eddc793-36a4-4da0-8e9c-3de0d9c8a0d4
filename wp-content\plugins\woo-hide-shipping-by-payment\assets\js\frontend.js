/**
 * Frontend JavaScript for WooCommerce Hide Shipping by Payment Method
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize frontend functionality
    var WooHideShippingFrontend = {

        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            // Listen for payment method changes
            $(document.body).on('change', 'input[name="payment_method"]', this.onPaymentMethodChange);

            // Listen for checkout updates
            $(document.body).on('updated_checkout', this.onCheckoutUpdated);

            // Listen for order review updates
            $(document.body).on('update_checkout', this.onUpdateCheckout);
        },

        onPaymentMethodChange: function() {
            var selectedPaymentMethod = $('input[name="payment_method"]:checked').val();

            if (selectedPaymentMethod) {
                // Trigger checkout update to refresh shipping methods
                $('body').trigger('update_checkout');
            }
        },

        onCheckoutUpdated: function() {
            // This event is triggered after the checkout has been updated
            // We can use this to perform any additional actions if needed
            console.log('Checkout updated - shipping methods refreshed');
        },

        onUpdateCheckout: function() {
            // This event is triggered when the checkout is being updated
            // The payment method change will be handled by WooCommerce's built-in functionality
            // which will call our PHP filter to hide shipping methods
        },

        // Optional: Manual AJAX call to get hidden methods (if needed for custom implementations)
        getHiddenMethodsForPayment: function(paymentMethod, callback) {
            $.ajax({
                url: wooHideShippingFrontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'woo_hide_shipping_update_checkout',
                    payment_method: paymentMethod,
                    nonce: wooHideShippingFrontend.nonce
                },
                success: function(response) {
                    if (response.success && typeof callback === 'function') {
                        callback(response.data.hidden_methods);
                    }
                },
                error: function() {
                    console.log('Error getting hidden shipping methods');
                }
            });
        },

        // Helper function to hide specific shipping methods (if needed for custom implementations)
        hideShippingMethods: function(methodsToHide) {
            if (!Array.isArray(methodsToHide) || methodsToHide.length === 0) {
                return;
            }

            // Hide shipping methods in the UI
            methodsToHide.forEach(function(methodId) {
                var $shippingOption = $('input[name="shipping_method[0]"][value*="' + methodId + '"]');
                if ($shippingOption.length > 0) {
                    $shippingOption.closest('li').hide();
                }
            });

            // If the currently selected shipping method is hidden, select the first available one
            var $selectedShipping = $('input[name="shipping_method[0]"]:checked');
            if ($selectedShipping.length > 0 && $selectedShipping.closest('li').is(':hidden')) {
                var $firstVisible = $('input[name="shipping_method[0]"]').filter(function() {
                    return $(this).closest('li').is(':visible');
                }).first();

                if ($firstVisible.length > 0) {
                    $firstVisible.prop('checked', true).trigger('change');
                }
            }
        },

        // Helper function to show all shipping methods (if needed for custom implementations)
        showAllShippingMethods: function() {
            $('input[name="shipping_method[0]"]').closest('li').show();
        }
    };

    // Initialize when document is ready
    WooHideShippingFrontend.init();

    // Also initialize when checkout fragments are updated
    $(document.body).on('updated_wc_div', function() {
        WooHideShippingFrontend.init();
    });
});