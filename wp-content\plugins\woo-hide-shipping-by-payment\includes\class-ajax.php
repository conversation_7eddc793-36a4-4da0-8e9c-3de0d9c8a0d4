<?php
/**
 * AJAX functionality for WooCommerce Hide Shipping by Payment Method
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooHideShipping_Ajax {

    /**
     * Constructor
     */
    public function __construct() {
        // Admin AJAX actions
        add_action('wp_ajax_woo_hide_shipping_delete_rule', array($this, 'ajax_delete_rule'));
        add_action('wp_ajax_woo_hide_shipping_toggle_status', array($this, 'ajax_toggle_status'));

        // Frontend AJAX actions
        add_action('wp_ajax_woo_hide_shipping_update_checkout', array($this, 'ajax_update_checkout'));
        add_action('wp_ajax_nopriv_woo_hide_shipping_update_checkout', array($this, 'ajax_update_checkout'));
    }

    /**
     * AJAX handler for deleting a rule
     */
    public function ajax_delete_rule() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'woo_hide_shipping_nonce')) {
            wp_die(__('Sicurezza non valida.', 'woo-hide-shipping-by-payment'));
        }

        // Check permissions
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Permessi insufficienti.', 'woo-hide-shipping-by-payment'));
        }

        $rule_id = intval($_POST['rule_id']);

        if (empty($rule_id)) {
            wp_send_json_error(__('ID regola non valido.', 'woo-hide-shipping-by-payment'));
        }

        $result = WooHideShipping_Database::delete_rule($rule_id);

        if ($result) {
            wp_send_json_success(__('Regola eliminata con successo.', 'woo-hide-shipping-by-payment'));
        } else {
            wp_send_json_error(__('Errore durante l\'eliminazione della regola.', 'woo-hide-shipping-by-payment'));
        }
    }

    /**
     * AJAX handler for toggling rule status
     */
    public function ajax_toggle_status() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'woo_hide_shipping_nonce')) {
            wp_die(__('Sicurezza non valida.', 'woo-hide-shipping-by-payment'));
        }

        // Check permissions
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Permessi insufficienti.', 'woo-hide-shipping-by-payment'));
        }

        $rule_id = intval($_POST['rule_id']);
        $new_status = sanitize_text_field($_POST['status']);

        if (empty($rule_id) || !in_array($new_status, array('active', 'inactive'))) {
            wp_send_json_error(__('Parametri non validi.', 'woo-hide-shipping-by-payment'));
        }

        // Get current rule
        $rule = WooHideShipping_Database::get_rule($rule_id);
        if (!$rule) {
            wp_send_json_error(__('Regola non trovata.', 'woo-hide-shipping-by-payment'));
        }

        // Update status
        $data = array(
            'rule_name' => $rule->rule_name,
            'payment_methods' => $rule->payment_methods,
            'shipping_methods' => $rule->shipping_methods,
            'status' => $new_status
        );

        $result = WooHideShipping_Database::update_rule($rule_id, $data);

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Stato aggiornato con successo.', 'woo-hide-shipping-by-payment'),
                'new_status' => $new_status
            ));
        } else {
            wp_send_json_error(__('Errore durante l\'aggiornamento dello stato.', 'woo-hide-shipping-by-payment'));
        }
    }

    /**
     * AJAX handler for updating checkout when payment method changes
     */
    public function ajax_update_checkout() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'woo_hide_shipping_frontend_nonce')) {
            wp_die(__('Sicurezza non valida.', 'woo-hide-shipping-by-payment'));
        }

        $payment_method = sanitize_text_field($_POST['payment_method']);

        if (empty($payment_method)) {
            wp_send_json_error(__('Metodo di pagamento non valido.', 'woo-hide-shipping-by-payment'));
        }

        // Update session with chosen payment method
        WC()->session->set('chosen_payment_method', $payment_method);

        // Get hidden shipping methods for this payment method
        $frontend = new WooHideShipping_Frontend();
        $hidden_methods = $frontend->get_hidden_methods_for_payment($payment_method);

        wp_send_json_success(array(
            'hidden_methods' => $hidden_methods,
            'payment_method' => $payment_method
        ));
    }
}