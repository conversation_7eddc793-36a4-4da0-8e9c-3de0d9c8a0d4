<?php
/**
 * Database operations for WooCommerce Hide Shipping by Payment Method
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooHideShipping_Database {

    /**
     * Constructor
     */
    public function __construct() {
        // Database operations are handled statically
    }

    /**
     * Create the database table for storing rules
     */
    public static function create_table() {
        global $wpdb;

        $table_name = WooHideShippingByPayment::get_table_name();

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            rule_name varchar(255) NOT NULL,
            payment_methods longtext NOT NULL,
            shipping_methods longtext NOT NULL,
            status enum('active','inactive') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get all rules
     */
    public static function get_rules($status = 'active') {
        global $wpdb;

        $table_name = WooHideShippingByPayment::get_table_name();

        if ($status === 'all') {
            $results = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC");
        } else {
            $results = $wpdb->get_results($wpdb->prepare("SELECT * FROM $table_name WHERE status = %s ORDER BY created_at DESC", $status));
        }

        // Unserialize the arrays
        if ($results) {
            foreach ($results as $rule) {
                $rule->payment_methods = maybe_unserialize($rule->payment_methods);
                $rule->shipping_methods = maybe_unserialize($rule->shipping_methods);
            }
        }

        return $results;
    }

    /**
     * Get a single rule by ID
     */
    public static function get_rule($id) {
        global $wpdb;

        $table_name = WooHideShippingByPayment::get_table_name();

        $rule = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id));

        if ($rule) {
            $rule->payment_methods = maybe_unserialize($rule->payment_methods);
            $rule->shipping_methods = maybe_unserialize($rule->shipping_methods);
        }

        return $rule;
    }

    /**
     * Insert a new rule
     */
    public static function insert_rule($data) {
        global $wpdb;

        $table_name = WooHideShippingByPayment::get_table_name();

        // Serialize arrays
        $data['payment_methods'] = maybe_serialize($data['payment_methods']);
        $data['shipping_methods'] = maybe_serialize($data['shipping_methods']);

        $result = $wpdb->insert(
            $table_name,
            array(
                'rule_name' => sanitize_text_field($data['rule_name']),
                'payment_methods' => $data['payment_methods'],
                'shipping_methods' => $data['shipping_methods'],
                'status' => sanitize_text_field($data['status'])
            ),
            array('%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
     * Update a rule
     */
    public static function update_rule($id, $data) {
        global $wpdb;

        $table_name = WooHideShippingByPayment::get_table_name();

        // Serialize arrays
        $data['payment_methods'] = maybe_serialize($data['payment_methods']);
        $data['shipping_methods'] = maybe_serialize($data['shipping_methods']);

        $result = $wpdb->update(
            $table_name,
            array(
                'rule_name' => sanitize_text_field($data['rule_name']),
                'payment_methods' => $data['payment_methods'],
                'shipping_methods' => $data['shipping_methods'],
                'status' => sanitize_text_field($data['status'])
            ),
            array('id' => $id),
            array('%s', '%s', '%s', '%s'),
            array('%d')
        );

        return $result !== false;
    }

    /**
     * Delete a rule
     */
    public static function delete_rule($id) {
        global $wpdb;

        $table_name = WooHideShippingByPayment::get_table_name();

        $result = $wpdb->delete(
            $table_name,
            array('id' => $id),
            array('%d')
        );

        return $result !== false;
    }

    /**
     * Get active rules for a specific payment method
     */
    public static function get_rules_for_payment_method($payment_method) {
        $rules = self::get_rules('active');
        $matching_rules = array();

        if ($rules) {
            foreach ($rules as $rule) {
                if (is_array($rule->payment_methods) && in_array($payment_method, $rule->payment_methods)) {
                    $matching_rules[] = $rule;
                }
            }
        }

        return $matching_rules;
    }
}