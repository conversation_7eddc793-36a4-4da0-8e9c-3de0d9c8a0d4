<?php
/**
 * Plugin Name: WooCommerce Nascondi spedizione con metodo di pagamento
 * Plugin URI: https://yourwebsite.com/
 * Description: Nascondi spedizione con metodo di pagamento specificato
 * Version: 1.0.0
 * Author: <PERSON> '<PERSON><PERSON><PERSON>' <PERSON>
 * Text Domain: woo-hide-shipping-by-payment
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.5
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WOO_HIDE_SHIPPING_VERSION', '1.0.0');
define('WOO_HIDE_SHIPPING_PLUGIN_FILE', __FILE__);
define('WOO_HIDE_SHIPPING_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WOO_HIDE_SHIPPING_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WOO_HIDE_SHIPPING_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class WooHideShippingByPayment {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Database table name
     */
    public static $table_name = 'woo_hide_shipping_rules';

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Load text domain
        load_plugin_textdomain('woo-hide-shipping-by-payment', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Include required files
        $this->includes();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Include required files
     */
    private function includes() {
        require_once WOO_HIDE_SHIPPING_PLUGIN_DIR . 'includes/class-database.php';
        require_once WOO_HIDE_SHIPPING_PLUGIN_DIR . 'includes/class-admin.php';
        require_once WOO_HIDE_SHIPPING_PLUGIN_DIR . 'includes/class-frontend.php';
        require_once WOO_HIDE_SHIPPING_PLUGIN_DIR . 'includes/class-ajax.php';
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(WOO_HIDE_SHIPPING_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(WOO_HIDE_SHIPPING_PLUGIN_FILE, array($this, 'deactivate'));

        // Initialize classes
        new WooHideShipping_Database();
        new WooHideShipping_Admin();
        new WooHideShipping_Frontend();
        new WooHideShipping_Ajax();
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create database table
        WooHideShipping_Database::create_table();

        // Set default options
        add_option('woo_hide_shipping_version', WOO_HIDE_SHIPPING_VERSION);

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        flush_rewrite_rules();
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('WooCommerce Hide Shipping by Payment Method requires WooCommerce to be installed and active.', 'woo-hide-shipping-by-payment'); ?></p>
        </div>
        <?php
    }

    /**
     * Get database table name with prefix
     */
    public static function get_table_name() {
        global $wpdb;
        return $wpdb->prefix . self::$table_name;
    }

    /**
     * Debug function to test database operations
     */
    public static function debug_database() {
        if (!current_user_can('manage_options')) {
            return;
        }

        global $wpdb;
        $table_name = self::get_table_name();

        echo "<h3>WooHideShipping Database Debug</h3>";

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
        echo "<p>Table exists: " . ($table_exists ? 'YES' : 'NO') . "</p>";

        if (!$table_exists) {
            echo "<p>Creating table...</p>";
            WooHideShipping_Database::create_table();
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
            echo "<p>Table exists after creation: " . ($table_exists ? 'YES' : 'NO') . "</p>";
        }

        if ($table_exists) {
            // Show table structure
            $columns = $wpdb->get_results("DESCRIBE {$table_name}");
            echo "<h4>Table Structure:</h4><ul>";
            foreach ($columns as $column) {
                echo "<li>{$column->Field} - {$column->Type}</li>";
            }
            echo "</ul>";

            // Test insert
            echo "<h4>Testing Insert:</h4>";
            $test_data = array(
                'rule_name' => 'Test Rule',
                'payment_methods' => array('bacs'),
                'shipping_methods' => array('flat_rate'),
                'status' => 'active'
            );

            $result = WooHideShipping_Database::insert_rule($test_data);
            echo "<p>Insert result: " . ($result ? "SUCCESS (ID: {$result})" : "FAILED") . "</p>";

            if ($wpdb->last_error) {
                echo "<p>Database error: " . $wpdb->last_error . "</p>";
            }
        }
    }
}

/**
 * Initialize the plugin
 */
function woo_hide_shipping_by_payment() {
    return WooHideShippingByPayment::get_instance();
}

// Start the plugin
woo_hide_shipping_by_payment();

// Debug hook - add ?woo_hide_shipping_debug=1 to any admin page to run debug
add_action('admin_init', function() {
    if (isset($_GET['woo_hide_shipping_debug']) && $_GET['woo_hide_shipping_debug'] == '1') {
        add_action('admin_notices', function() {
            WooHideShippingByPayment::debug_database();
        });
    }
});